 If you are still having issues trying to POC this, I found a very hacky and tricky way to do this.(You don't even need to build the program)

The exact steps are very important
**Steps to reproduce**
1.) Add this script to the workspace to help set it up and run the script
```bash
#!/bin/bash

mkdir -p target/idl
mkdir -p target/deploy
mkdir -p tests/

# Downgrade to a compatible version of Anchor and download the IDL
avm use 0.29.0
anchor idl fetch 6kpxYKjqe8z66hnDHbbjhEUxha46cnz2UqrneGECmFBg --out target/idl/bridge_program.json

# Clone program from devnet and ensure they are executable
solana program dump 6kpxYKjqe8z66hnDHbbjhEUxha46cnz2UqrneGECmFBg --url devnet target/deploy/bridge.so 
chmod +x target/deploy/bridge.so

# Clone Metaplex program from devnet
solana program dump metaqbxxUerdq28cj1RbAWkYQm3ybzjb6a8bt518x1s --url devnet target/deploy/metaplex.so
chmod +x target/deploy/metaplex.so

# write test files
cp -r scripts/constants.ts tests/constants.ts
cp -r scripts/utils.ts tests/utils.ts
cp -r scripts/types.ts tests/types.ts
cp -r scripts/devnet/solana/initialize.ts tests/initialize.ts
```
2.) Modify the `Anchor.toml` and `tests/initialize.ts` as follows (This must be done after the above script)
```toml
[toolchain]

[features]
seeds = false
skip-lint = false

[programs.localnet]
bridge_program = "6kpxYKjqe8z66hnDHbbjhEUxha46cnz2UqrneGECmFBg"

[registry]
url = "https://api.apr.dev"

[provider]
# cluster = "https://special-radial-slug.solana-devnet.quiknode.pro/6a226d3d77caa81d31c4cc180c4a2f78d4e922b7/"
# wallet = "/Users/<USER>/.config/solana/id.json"
cluster = "localnet"
wallet = "./keys/devnet/solana_manager.json"

[[test.genesis]]
address = "metaqbxxUerdq28cj1RbAWkYQm3ybzjb6a8bt518x1s"
program = "target/deploy/metaplex.so"

[[test.genesis]]
address = "6kpxYKjqe8z66hnDHbbjhEUxha46cnz2UqrneGECmFBg"
program = "target/deploy/bridge.so"

[scripts]
test = "yarn run ts-mocha -p ./tsconfig.json -t 1000000 tests/**/*.ts"
```


```typescript
//tests/initialize.ts
import * as anchor from "@coral-xyz/anchor";
import {
  clusterApiUrl,
  Connection,
  LAMPORTS_PER_SOL,
  PublicKey,
  sendAndConfirmTransaction,
  SystemProgram,
} from "@solana/web3.js";
import {
  loadKeypairFromFile,
  log,
  newTransactionWithComputeUnitPriceAndLimit,
} from "./utils";
import BridgeHandlerProgramIDL from "./../target/idl/bridge_program.json";
import {
  BRIDGE_HANDLER_SOLANA_NONCE,
  BRIDGE_PROGRAM_ID,
} from "./constants";

const SOLANA_MANAGER = loadKeypairFromFile("./keys/devnet/solana_manager.json");
const SOLANA_OPERATOR = loadKeypairFromFile(
  "./keys/devnet/solana_operator.json"
);
const FEE_VAULT = loadKeypairFromFile("./keys/devnet/fee_vault.json");
    describe("anchor-test", () => {

        console.log(`signer wallet public key is: ${SOLANA_MANAGER.publicKey}`);
        const provider = anchor.AnchorProvider.env();
        anchor.setProvider(provider);

        const program = new anchor.Program(
            BridgeHandlerProgramIDL as anchor.Idl,
            BRIDGE_PROGRAM_ID,
            provider
        );
            
        it("is initialized", async () => {
        console.log(
            `signer wallet balance is: ${
            (await provider.connection.getBalance(SOLANA_MANAGER.publicKey)) / LAMPORTS_PER_SOL
            } SOL`
        );
        const init_nonce = new anchor.BN(BRIDGE_HANDLER_SOLANA_NONCE);

        const [bridgeHandler, bump] = PublicKey.findProgramAddressSync(
            [Buffer.from("bridge_handler"), init_nonce.toArrayLike(Buffer, "be", 8)],
            program.programId
        );

        const [guardianInfo, guardianBump] = PublicKey.findProgramAddressSync(
            [Buffer.from("guardian_info"), bridgeHandler.toBuffer()],
            program.programId
        );

        console.log(`bridge handler is: ${bridgeHandler.toString()}`);
        console.log(`guardian info is: ${guardianInfo.toString()}`);

        let tx = newTransactionWithComputeUnitPriceAndLimit();
        console.log("Size of program accounts:", await program.provider.connection.getBalance(program.programId));
        const initInst = await program.methods
            .initialize(init_nonce, 1) // 1 for Solana, 2 for Solayer
            .accounts({
            signer: SOLANA_MANAGER.publicKey,
            bridgeHandler,
            guardianInfo,
            feeVault: FEE_VAULT.publicKey,
            manager: SOLANA_MANAGER.publicKey,
            operator: SOLANA_OPERATOR.publicKey,
            systemProgram: SystemProgram.programId,
            })
            .instruction();

        tx.add(initInst);

        try {
            await program.provider.sendAndConfirm(tx).then(log);
        } catch (error) {
            console.error(error);
        }

    });
});
```
3.) Install dependencies with `npm i`
4.) Test with `anchor test --skip-build`