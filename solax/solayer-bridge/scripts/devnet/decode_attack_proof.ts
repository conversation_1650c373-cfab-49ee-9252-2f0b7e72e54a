/**
 * Decode Attack Proof - Show exactly what the attacker achieved
 */

import * as anchor from "@coral-xyz/anchor";
import {
  clusterApiUrl,
  Connection,
  PublicKey,
} from "@solana/web3.js";
import {
  loadKeypairFromFile,
} from "../utils";
import BridgeHandlerProgramIDL from "../../target/idl/bridge_program.json";
import {
  BRIDGE_PROGRAM_ID,
} from "../constants";

async function main() {
  console.log("🔍 DECODING ATTACK PROOF - COMPLETE EVIDENCE");
  console.log("=" .repeat(60));
  
  const connection = new Connection(clusterApiUrl("devnet"));
  
  // The compromised bridge account
  const compromisedBridgeHandler = new PublicKey("Aww9N81mBWVawchFVD9C5ef6ekVUncDsmhfN6EYLPnzy");
  const attackTransaction = "4EBtX4QqaV2cdc6Xfz4sHJGp2wLYQLbwXk6m6x5Egt8ZkhxsmFp*************************************";
  
  console.log("\n📍 ATTACK EVIDENCE:");
  console.log(`   Compromised Bridge: ${compromisedBridgeHandler}`);
  console.log(`   Attack Transaction: ${attackTransaction}`);
  console.log(`   Explorer: https://explorer.solana.com/tx/${attackTransaction}?cluster=devnet`);
  
  // Setup program to decode the account
  const attacker = loadKeypairFromFile("./keys/devnet/solana_manager.json");
  const provider = new anchor.AnchorProvider(
    connection,
    new anchor.Wallet(attacker),
    { commitment: "confirmed" }
  );
  
  const program = new anchor.Program(
    BridgeHandlerProgramIDL as anchor.Idl,
    BRIDGE_PROGRAM_ID,
    provider
  );
  
  // Decode the compromised bridge state
  console.log("\n🔥 COMPROMISED BRIDGE STATE:");
  
  try {
    const bridgeAccount = await program.account.bridgeHandler.fetch(compromisedBridgeHandler);
    
    console.log("   📊 DECODED BRIDGE DATA:");
    console.log(`      Manager: ${bridgeAccount.manager}`);
    console.log(`      Operator: ${bridgeAccount.operator}`);
    console.log(`      Fee Vault: ${bridgeAccount.feeVault}`);
    console.log(`      Chain: ${bridgeAccount.chain}`);
    console.log(`      Init Nonce: ${bridgeAccount.initNonce}`);
    console.log(`      Current Nonce: ${bridgeAccount.nonce}`);
    console.log(`      Paused: ${bridgeAccount.pause}`);
    console.log(`      Guardian Threshold: ${bridgeAccount.guardianThreshold}`);
    console.log(`      Guardian Info: ${bridgeAccount.guardianInfo}`);
    
    // Show the attack proof
    console.log("\n🚨 ATTACK PROOF ANALYSIS:");
    
    const attackerAddress = new PublicKey("736WtbECbhkBAh8Rsg4RTR2c8u4VZfJHRyu46k6iBKWX");
    const attackerFeeVault = new PublicKey("EvBWYM78Kv1Mo2AKUBq5ype11qmXZgF4hWytfemaHvnA");
    
    const managerCompromised = (bridgeAccount.manager as PublicKey).equals(attackerAddress);
    const operatorCompromised = (bridgeAccount.operator as PublicKey).equals(attackerAddress);
    const feeVaultCompromised = (bridgeAccount.feeVault as PublicKey).equals(attackerFeeVault);
    
    console.log(`   Manager Compromised: ${managerCompromised ? '🔥 YES' : '✅ NO'}`);
    console.log(`   Operator Compromised: ${operatorCompromised ? '🔥 YES' : '✅ NO'}`);
    console.log(`   Fee Vault Compromised: ${feeVaultCompromised ? '🔥 YES' : '✅ NO'}`);
    
    if (managerCompromised && operatorCompromised && feeVaultCompromised) {
      console.log("\n   🚨 CONFIRMED: COMPLETE BRIDGE COMPROMISE!");
      console.log("   🔥 Attacker has TOTAL control of the bridge");
    }
    
    // Show what this means
    console.log("\n💥 ATTACK IMPACT BREAKDOWN:");
    console.log("   🔥 Manager Control:");
    console.log("      • Can update all bridge parameters");
    console.log("      • Can change manager and operator roles");
    console.log("      • Can modify guardian settings");
    console.log("      • Can pause/unpause bridge operations");
    
    console.log("   🔥 Operator Control:");
    console.log("      • Can execute bridge operations");
    console.log("      • Can process bridge transactions");
    console.log("      • Can manipulate bridge state");
    
    console.log("   🔥 Fee Vault Control:");
    console.log("      • ALL bridge fees go to attacker");
    console.log("      • Unlimited fee drainage capability");
    console.log("      • Complete financial takeover");
    
  } catch (error: any) {
    console.log(`   ❌ Could not decode bridge account: ${error.message}`);
  }
  
  // Show the vulnerability that enabled this
  console.log("\n🔍 VULNERABILITY ROOT CAUSE:");
  console.log("   File: programs/bridge/src/contexts/initialize.rs");
  console.log("   Lines 11-12:");
  console.log("   ```rust");
  console.log("   #[account(mut)]");
  console.log("   signer: Signer<'info>,  // ❌ NO ACCESS CONTROL!");
  console.log("   ```");
  console.log("");
  console.log("   Lines 29-34:");
  console.log("   ```rust");
  console.log("   /// CHECK: no check needed  // ❌ NO VALIDATION!");
  console.log("   fee_vault: AccountInfo<'info>,");
  console.log("   manager: AccountInfo<'info>,");
  console.log("   operator: AccountInfo<'info>,");
  console.log("   ```");
  
  // Show the attack timeline
  console.log("\n⏰ ATTACK TIMELINE:");
  console.log("   1. 🕐 Attacker identifies vulnerable initialize function");
  console.log("   2. 🕑 Attacker generates malicious fee vault keypair");
  console.log("   3. 🕒 Attacker calculates target PDA addresses");
  console.log("   4. 🕓 Attacker creates malicious initialize transaction");
  console.log("   5. 🕔 Attacker sets themselves as manager, operator, fee_vault");
  console.log("   6. 🕕 Transaction executes successfully");
  console.log("   7. 🕖 Bridge is now completely controlled by attacker");
  console.log("   8. 🕗 Legitimate initialization is now impossible");
  
  // Show the proof links
  console.log("\n🔗 VERIFICATION LINKS:");
  console.log(`   Transaction: https://explorer.solana.com/tx/${attackTransaction}?cluster=devnet`);
  console.log(`   Bridge Account: https://explorer.solana.com/address/${compromisedBridgeHandler}?cluster=devnet`);
  console.log(`   Attacker Address: https://explorer.solana.com/address/736WtbECbhkBAh8Rsg4RTR2c8u4VZfJHRyu46k6iBKWX?cluster=devnet`);
  console.log(`   Attacker Fee Vault: https://explorer.solana.com/address/EvBWYM78Kv1Mo2AKUBq5ype11qmXZgF4hWytfemaHvnA?cluster=devnet`);
  
  console.log("\n" + "=".repeat(60));
  console.log("🎯 ATTACK PROOF COMPLETE");
  console.log("   This demonstrates a successful front-run initialization attack");
  console.log("   The attacker now has complete control of the bridge system");
  console.log("=".repeat(60));
}

main().then(() => process.exit());
