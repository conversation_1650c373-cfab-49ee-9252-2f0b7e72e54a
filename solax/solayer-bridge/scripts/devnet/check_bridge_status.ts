/**
 * Bridge Status Checker for Devnet
 * 
 * This script checks the current status of the bridge on Solana devnet
 * and provides detailed information about accounts, transactions, and state.
 */

import * as anchor from "@coral-xyz/anchor";
import {
  clusterApiUrl,
  Connection,
  PublicKey,
} from "@solana/web3.js";
import {
  loadKeypairFromFile,
} from "../utils";
import BridgeHandlerProgramIDL from "../../target/idl/bridge_program.json";
import {
  BRIDGE_PROGRAM_ID,
  BRIDGE_HANDLER_SOLANA_NONCE,
} from "../constants";

async function main() {
  console.log("🔍 BRIDGE STATUS CHECKER - SOLANA DEVNET");
  console.log("=" .repeat(60));
  
  const connection = new Connection(clusterApiUrl("devnet"));
  
  // Step 1: Calculate bridge PDAs
  console.log("\n📍 BRIDGE PDA ADDRESSES:");
  const init_nonce = new anchor.BN(BRIDGE_HANDLER_SOLANA_NONCE);
  
  const [bridgeHandler, bridgeBump] = PublicKey.findProgramAddressSync(
    [Buffer.from("bridge_handler"), init_nonce.toArrayLike(<PERSON>uff<PERSON>, "be", 8)],
    BRIDGE_PROGRAM_ID
  );
  
  const [guardianInfo, guardianBump] = PublicKey.findProgramAddressSync(
    [Buffer.from("guardian_info"), bridgeHandler.toBuffer()],
    BRIDGE_PROGRAM_ID
  );
  
  console.log(`   Bridge Handler: ${bridgeHandler}`);
  console.log(`   Guardian Info: ${guardianInfo}`);
  console.log(`   Program ID: ${BRIDGE_PROGRAM_ID}`);
  console.log(`   Init Nonce: ${BRIDGE_HANDLER_SOLANA_NONCE}`);
  
  // Step 2: Check account existence and balances
  console.log("\n💰 ACCOUNT STATUS:");
  
  const bridgeAccountInfo = await connection.getAccountInfo(bridgeHandler);
  const guardianAccountInfo = await connection.getAccountInfo(guardianInfo);
  
  if (bridgeAccountInfo) {
    console.log(`   ✅ Bridge Handler exists`);
    console.log(`      Balance: ${bridgeAccountInfo.lamports / 1e9} SOL`);
    console.log(`      Owner: ${bridgeAccountInfo.owner}`);
    console.log(`      Data Length: ${bridgeAccountInfo.data.length} bytes`);
    console.log(`      Executable: ${bridgeAccountInfo.executable}`);
  } else {
    console.log(`   ❌ Bridge Handler does not exist`);
  }
  
  if (guardianAccountInfo) {
    console.log(`   ✅ Guardian Info exists`);
    console.log(`      Balance: ${guardianAccountInfo.lamports / 1e9} SOL`);
    console.log(`      Owner: ${guardianAccountInfo.owner}`);
    console.log(`      Data Length: ${guardianAccountInfo.data.length} bytes`);
  } else {
    console.log(`   ❌ Guardian Info does not exist`);
  }
  
  // Step 3: Parse bridge state if it exists
  if (bridgeAccountInfo) {
    console.log("\n🎯 BRIDGE STATE DETAILS:");
    
    try {
      const legitimateManager = loadKeypairFromFile("./keys/devnet/solana_manager.json");
      
      const provider = new anchor.AnchorProvider(
        connection,
        new anchor.Wallet(legitimateManager),
        { commitment: "confirmed" }
      );
      
      const program = new anchor.Program(
        BridgeHandlerProgramIDL as anchor.Idl,
        BRIDGE_PROGRAM_ID,
        provider
      );
      
      const bridgeAccount = await program.account.bridgeHandler.fetch(bridgeHandler);
      
      console.log(`   Manager: ${bridgeAccount.manager}`);
      console.log(`   Operator: ${bridgeAccount.operator}`);
      console.log(`   Fee Vault: ${bridgeAccount.feeVault}`);
      console.log(`   Chain: ${bridgeAccount.chain}`);
      console.log(`   Init Nonce: ${bridgeAccount.initNonce}`);
      console.log(`   Current Nonce: ${bridgeAccount.nonce}`);
      console.log(`   Paused: ${bridgeAccount.pause}`);
      console.log(`   Guardian Threshold: ${bridgeAccount.guardianThreshold}`);
      console.log(`   Guardian Info: ${bridgeAccount.guardianInfo}`);
      
      // Step 4: Verify legitimate control
      console.log("\n🔐 CONTROL VERIFICATION:");
      
      const legitimateOperator = loadKeypairFromFile("./keys/devnet/solana_operator.json");
      const legitimateFeeVault = loadKeypairFromFile("./keys/devnet/fee_vault.json");
      
      const managerMatch = (bridgeAccount.manager as PublicKey).equals(legitimateManager.publicKey);
      const operatorMatch = (bridgeAccount.operator as PublicKey).equals(legitimateOperator.publicKey);
      const feeVaultMatch = (bridgeAccount.feeVault as PublicKey).equals(legitimateFeeVault.publicKey);
      
      console.log(`   Manager Control: ${managerMatch ? '✅ LEGITIMATE' : '🔥 COMPROMISED'}`);
      console.log(`   Operator Control: ${operatorMatch ? '✅ LEGITIMATE' : '🔥 COMPROMISED'}`);
      console.log(`   Fee Vault Control: ${feeVaultMatch ? '✅ LEGITIMATE' : '🔥 COMPROMISED'}`);
      
      if (managerMatch && operatorMatch && feeVaultMatch) {
        console.log(`   🎯 OVERALL STATUS: ✅ BRIDGE IS SECURE`);
      } else {
        console.log(`   🚨 OVERALL STATUS: 🔥 BRIDGE IS COMPROMISED!`);
      }
      
    } catch (error: any) {
      console.log(`   ❌ Could not parse bridge state: ${error.message}`);
    }
  }
  
  // Step 5: Check recent transactions
  console.log("\n📊 RECENT TRANSACTION HISTORY:");
  
  try {
    const signatures = await connection.getSignaturesForAddress(bridgeHandler, { limit: 5 });
    
    if (signatures.length > 0) {
      console.log(`   Found ${signatures.length} recent transactions:`);
      
      for (let i = 0; i < signatures.length; i++) {
        const sig = signatures[i];
        console.log(`   ${i + 1}. ${sig.signature}`);
        console.log(`      Slot: ${sig.slot}`);
        console.log(`      Status: ${sig.err ? 'FAILED' : 'SUCCESS'}`);
        console.log(`      Block Time: ${sig.blockTime ? new Date(sig.blockTime * 1000).toISOString() : 'Unknown'}`);
        console.log(`      Explorer: https://explorer.solana.com/tx/${sig.signature}?cluster=devnet`);
        console.log("");
      }
    } else {
      console.log(`   No recent transactions found`);
    }
    
  } catch (error: any) {
    console.log(`   ❌ Could not fetch transaction history: ${error.message}`);
  }
  
  // Step 6: Check program account
  console.log("\n🏗️  PROGRAM STATUS:");
  
  const programAccount = await connection.getAccountInfo(BRIDGE_PROGRAM_ID);
  if (programAccount) {
    console.log(`   ✅ Bridge Program deployed`);
    console.log(`      Address: ${BRIDGE_PROGRAM_ID}`);
    console.log(`      Balance: ${programAccount.lamports / 1e9} SOL`);
    console.log(`      Executable: ${programAccount.executable}`);
    console.log(`      Owner: ${programAccount.owner}`);
  } else {
    console.log(`   ❌ Bridge Program not found`);
  }
  
  // Step 7: Network status
  console.log("\n🌐 NETWORK STATUS:");
  
  try {
    const slot = await connection.getSlot();
    const blockTime = await connection.getBlockTime(slot);
    const epochInfo = await connection.getEpochInfo();
    
    console.log(`   Current Slot: ${slot}`);
    console.log(`   Block Time: ${blockTime ? new Date(blockTime * 1000).toISOString() : 'Unknown'}`);
    console.log(`   Epoch: ${epochInfo.epoch}`);
    console.log(`   Slot Index: ${epochInfo.slotIndex}/${epochInfo.slotsInEpoch}`);
    
  } catch (error: any) {
    console.log(`   ❌ Could not fetch network status: ${error.message}`);
  }
  
  // Step 8: Useful links
  console.log("\n🔗 USEFUL LINKS:");
  console.log(`   Bridge Handler: https://explorer.solana.com/address/${bridgeHandler}?cluster=devnet`);
  console.log(`   Guardian Info: https://explorer.solana.com/address/${guardianInfo}?cluster=devnet`);
  console.log(`   Program: https://explorer.solana.com/address/${BRIDGE_PROGRAM_ID}?cluster=devnet`);
  console.log(`   Solana FM: https://solana.fm/address/${bridgeHandler}?cluster=devnet`);
  
  console.log("\n" + "=".repeat(60));
  console.log("✅ STATUS CHECK COMPLETE");
  console.log("=".repeat(60));
}

main().then(() => process.exit());
